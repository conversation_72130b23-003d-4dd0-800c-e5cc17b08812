<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Calcul Précis - <PERSON><PERSON> Ouvrés avec Heures</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 1000px; margin: 0 auto; }
        .test-case { 
            background: white; 
            padding: 20px; 
            margin: 15px 0; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 5px solid #3498db;
        }
        .result { font-weight: bold; margin: 10px 0; }
        .exact { color: #2980b9; }
        .rounded { color: #27ae60; }
        .expected { color: #8e44ad; }
        .match { color: #27ae60; }
        .fail { color: #e74c3c; }
        .details { background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0; font-size: 0.9em; }
        h1 { color: #2c3e50; text-align: center; }
        h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px; }
        .summary { background: #e8f5e8; padding: 15px; border-radius: 10px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test de Calcul Précis avec Heures</h1>
        <div id="results"></div>
    </div>

    <script>
        // Copie des fonctions de calcul depuis script.js
        const holidays2025 = [
            "2025-01-01", "2025-03-31", "2025-04-04", "2025-05-01", "2025-05-29",
            "2025-06-07", "2025-06-09", "2025-06-26", "2025-08-15", "2025-09-05",
            "2025-11-01", "2025-12-25"
        ];

        function parseDate(dateStr) {
            if (!dateStr) return null;
            const formats = [
                /^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{2})$/,
                /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,
                /^(\d{4})-(\d{1,2})-(\d{1,2})T(\d{1,2}):(\d{2})$/
            ];
            
            for (let format of formats) {
                const match = dateStr.toString().match(format);
                if (match) {
                    if (format === formats[2]) {
                        return new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]), 
                                      parseInt(match[4]), parseInt(match[5]));
                    } else {
                        const day = parseInt(match[1]);
                        const month = parseInt(match[2]) - 1;
                        const year = parseInt(match[3]);
                        const hour = match[4] ? parseInt(match[4]) : 0;
                        const minute = match[5] ? parseInt(match[5]) : 0;
                        return new Date(year, month, day, hour, minute);
                    }
                }
            }
            return null;
        }

        function isWorkingDay(date) {
            const dayOfWeek = date.getDay();
            if (dayOfWeek === 0 || dayOfWeek === 6) return false;
            
            const dateStr = date.getFullYear() + '-' + 
                           String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                           String(date.getDate()).padStart(2, '0');
            
            return !holidays2025.includes(dateStr);
        }

        function calculateExactWorkingDays(startDate, endDate) {
            if (startDate > endDate) return 0;
            
            const totalHours = (endDate - startDate) / (1000 * 60 * 60);
            const totalCalendarDays = totalHours / 24;
            
            let workingDaysCount = 0;
            let currentDate = new Date(startDate);
            currentDate.setDate(currentDate.getDate() + 1);
            currentDate.setHours(0, 0, 0, 0);
            
            let endDateOnly = new Date(endDate);
            endDateOnly.setHours(0, 0, 0, 0);
            
            while (currentDate <= endDateOnly) {
                if (isWorkingDay(currentDate)) {
                    workingDaysCount++;
                }
                currentDate.setDate(currentDate.getDate() + 1);
            }
            
            if (totalCalendarDays < 1) {
                if (isWorkingDay(startDate)) {
                    return totalCalendarDays;
                } else {
                    return 0;
                }
            }
            
            const totalCalendarDaysInt = Math.ceil(totalCalendarDays);
            let totalCalendarWorkingDays = 0;
            
            currentDate = new Date(startDate);
            for (let i = 0; i < totalCalendarDaysInt; i++) {
                if (isWorkingDay(currentDate)) {
                    totalCalendarWorkingDays++;
                }
                currentDate.setDate(currentDate.getDate() + 1);
            }
            
            if (totalCalendarWorkingDays === 0) return 0;
            
            const workingDayRatio = totalCalendarWorkingDays / totalCalendarDaysInt;
            return totalCalendarDays * workingDayRatio;
        }

        function calculateWorkingDays(startDate, endDate) {
            if (startDate > endDate) {
                return { exact: 0, rounded: 0 };
            }
            
            let exactWorkingDays = calculateExactWorkingDays(startDate, endDate);
            let roundedDays = Math.round(exactWorkingDays);
            
            return {
                exact: Math.round(exactWorkingDays * 100) / 100,
                rounded: roundedDays
            };
        }

        // Cas de test avec l'exemple spécifique
        const testCases = [
            {
                name: "Exemple spécifique : 02/06/2025 16:57 → 05/06/2025 11:47",
                start: "2025-06-02T16:57",
                end: "2025-06-05T11:47",
                expectedExact: 2.78, // Approximation
                expectedRounded: 3,
                description: "Lundi 16:57 → Jeudi 11:47 (2 jours complets + fraction)"
            },
            {
                name: "Test simple : même jour",
                start: "2025-06-02T09:00",
                end: "2025-06-02T17:00",
                expectedExact: 0.33,
                expectedRounded: 0,
                description: "8 heures dans la même journée"
            },
            {
                name: "Test : 1 jour complet",
                start: "2025-06-02T09:00",
                end: "2025-06-03T09:00",
                expectedExact: 1.0,
                expectedRounded: 1,
                description: "Exactement 24 heures, 1 jour ouvré"
            },
            {
                name: "Test : avec week-end",
                start: "2025-06-06T15:00",
                end: "2025-06-09T10:00",
                expectedExact: 1.0,
                expectedRounded: 1,
                description: "Vendredi 15h → Lundi 10h (week-end exclu)"
            },
            {
                name: "Test : fraction < 1",
                start: "2025-06-02T14:00",
                end: "2025-06-02T18:00",
                expectedExact: 0.17,
                expectedRounded: 0,
                description: "4 heures = 0.17 jour"
            }
        ];

        function formatDate(date) {
            return date.toLocaleDateString('fr-FR') + ' ' + date.toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'});
        }

        function runTests() {
            const resultsDiv = document.getElementById('results');
            let allPassed = true;
            let passedCount = 0;

            testCases.forEach((testCase, index) => {
                const startDate = parseDate(testCase.start);
                const endDate = parseDate(testCase.end);
                const result = calculateWorkingDays(startDate, endDate);
                
                const exactMatch = Math.abs(result.exact - testCase.expectedExact) < 0.1;
                const roundedMatch = result.rounded === testCase.expectedRounded;
                const passed = exactMatch && roundedMatch;
                
                if (passed) passedCount++;
                if (!passed) allPassed = false;

                // Calculs détaillés
                const totalHours = (endDate - startDate) / (1000 * 60 * 60);
                const totalDays = totalHours / 24;

                const testDiv = document.createElement('div');
                testDiv.className = 'test-case';
                testDiv.innerHTML = `
                    <h2>${testCase.name} ${passed ? '✅' : '❌'}</h2>
                    <p><strong>Description:</strong> ${testCase.description}</p>
                    <p><strong>Période:</strong> ${formatDate(startDate)} → ${formatDate(endDate)}</p>
                    
                    <div class="details">
                        <strong>Calculs détaillés :</strong><br>
                        • Durée totale : ${Math.round(totalHours * 100) / 100} heures (${Math.round(totalDays * 100) / 100} jours calendaires)<br>
                        • Jour début : ${startDate.toLocaleDateString('fr-FR', { weekday: 'long' })}<br>
                        • Jour fin : ${endDate.toLocaleDateString('fr-FR', { weekday: 'long' })}
                    </div>
                    
                    <div class="result">
                        <div class="exact">🔢 Délai exact calculé : ${result.exact} jours 
                            ${exactMatch ? '<span class="match">✅</span>' : '<span class="fail">❌ (attendu: ' + testCase.expectedExact + ')</span>'}
                        </div>
                        <div class="rounded">🔄 Délai arrondi calculé : ${result.rounded} jours 
                            ${roundedMatch ? '<span class="match">✅</span>' : '<span class="fail">❌ (attendu: ' + testCase.expectedRounded + ')</span>'}
                        </div>
                    </div>
                `;
                resultsDiv.appendChild(testDiv);
            });

            // Résumé
            const summaryDiv = document.createElement('div');
            summaryDiv.className = 'summary';
            summaryDiv.innerHTML = `
                <h2>${allPassed ? '🎉 Tous les tests sont réussis !' : '⚠️ Certains tests ont échoué'}</h2>
                <p><strong>Tests exécutés :</strong> ${testCases.length}</p>
                <p><strong>Tests réussis :</strong> ${passedCount}</p>
                <p><strong>Taux de réussite :</strong> ${Math.round((passedCount / testCases.length) * 100)}%</p>
                
                <h3>📋 Règles de calcul appliquées :</h3>
                <ul>
                    <li>Calcul exact basé sur la durée totale en heures</li>
                    <li>Prise en compte du ratio jours ouvrés / jours calendaires</li>
                    <li>Arrondi mathématique standard (0.5 → 1)</li>
                    <li>Exclusion des week-ends et jours fériés</li>
                </ul>
            `;
            resultsDiv.appendChild(summaryDiv);
        }

        // Lancer les tests au chargement
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
