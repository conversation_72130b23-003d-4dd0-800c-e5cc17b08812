// Liste des jours fériés au Sénégal pour 2025
const holidays2025 = [
    "2025-01-01",  // <PERSON><PERSON> <PERSON>'<PERSON>
    "2025-03-31",  // <PERSON><PERSON> (Estimation, Aïd al-Fitr/Korité)
    "2025-04-04",  // Fête de l'Indépendance
    "2025-05-01",  // Fête du Travail
    "2025-05-29",  // Ascension
    "2025-06-07",  // <PERSON><PERSON><PERSON> (Tabaski)
    "2025-06-09",  // Lundi de Pentecôte
    "2025-06-26",  // Ashura (Tamkharit)
    "2025-08-15",  // Assomption
    "2025-09-05",  // Anniversaire du Prophète (Mouloud)
    "2025-11-01",  // Toussaint
    "2025-12-25",  // Noël
];

let processedData = [];

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
});

function setupEventListeners() {
    const uploadZone = document.getElementById('uploadZone');
    const fileInput = document.getElementById('fileInput');
    const exportBtn = document.getElementById('exportBtn');
    const calculateTestBtn = document.getElementById('calculateTestBtn');
    const useCurrentDate = document.getElementById('useCurrentDate');
    const testEndDate = document.getElementById('testEndDate');
    const testEndDateText = document.getElementById('testEndDateText');

    // Drag & Drop
    uploadZone.addEventListener('dragover', handleDragOver);
    uploadZone.addEventListener('dragleave', handleDragLeave);
    uploadZone.addEventListener('drop', handleDrop);
    uploadZone.addEventListener('click', () => fileInput.click());

    // File input
    fileInput.addEventListener('change', handleFileSelect);

    // Export button
    exportBtn.addEventListener('click', exportToExcel);

    // Test calculator
    calculateTestBtn.addEventListener('click', calculateTestDates);

    // Checkbox pour date actuelle
    useCurrentDate.addEventListener('change', function() {
        testEndDate.disabled = this.checked;
        testEndDateText.disabled = this.checked;
        if (this.checked) {
            testEndDate.style.opacity = '0.5';
            testEndDateText.style.opacity = '0.5';
        } else {
            testEndDate.style.opacity = '1';
            testEndDateText.style.opacity = '1';
        }
    });

    // Gestion des champs de saisie de dates
    setupDateInputs();
}

function setupDateInputs() {
    const startTextInput = document.getElementById('testStartDateText');
    const endTextInput = document.getElementById('testEndDateText');
    const startDateInput = document.getElementById('testStartDate');
    const endDateInput = document.getElementById('testEndDate');
    const toggleStartBtn = document.getElementById('toggleStartPicker');
    const toggleEndBtn = document.getElementById('toggleEndPicker');

    // Validation en temps réel pour les champs texte
    startTextInput.addEventListener('input', function() {
        validateAndSyncDateInput(this, startDateInput, 'start');
    });

    endTextInput.addEventListener('input', function() {
        validateAndSyncDateInput(this, endDateInput, 'end');
    });

    // Synchronisation des sélecteurs vers les champs texte
    startDateInput.addEventListener('change', function() {
        syncDateTimeToText(this, startTextInput);
    });

    endDateInput.addEventListener('change', function() {
        syncDateTimeToText(this, endTextInput);
    });

    // Boutons de basculement
    toggleStartBtn.addEventListener('click', function() {
        toggleDateInputMode('start');
    });

    toggleEndBtn.addEventListener('click', function() {
        toggleDateInputMode('end');
    });

    // Validation initiale
    validateAndSyncDateInput(startTextInput, startDateInput, 'start');
    validateAndSyncDateInput(endTextInput, endDateInput, 'end');
}

function handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('dragover');
}

function handleDragLeave(e) {
    e.currentTarget.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        processFile(files[0]);
    }
}

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        processFile(file);
    }
}

function processFile(file) {
    showLoading(true);
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            let data;
            const fileName = file.name.toLowerCase();
            
            if (fileName.endsWith('.csv')) {
                data = parseCSV(e.target.result);
            } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
                data = parseExcel(e.target.result);
            } else {
                throw new Error('Format de fichier non supporté');
            }
            
            processData(data);
        } catch (error) {
            alert('Erreur lors du traitement du fichier: ' + error.message);
            showLoading(false);
        }
    };
    
    if (file.name.toLowerCase().endsWith('.csv')) {
        reader.readAsText(file);
    } else {
        reader.readAsArrayBuffer(file);
    }
}

function parseCSV(text) {
    const lines = text.split('\n');
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const data = [];
    
    for (let i = 1; i < lines.length; i++) {
        if (lines[i].trim()) {
            const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
            const row = {};
            headers.forEach((header, index) => {
                row[header] = values[index] || '';
            });
            data.push(row);
        }
    }
    
    return data;
}

function parseExcel(buffer) {
    const workbook = XLSX.read(buffer, { type: 'array' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    return XLSX.utils.sheet_to_json(worksheet);
}

function processData(data) {
    processedData = [];
    let totalDays = 0;
    let validRows = 0;
    let minDays = Infinity;
    let maxDays = -Infinity;
    
    data.forEach((row, index) => {
        try {
            const dateInpute = parseDate(row.dateInpute);
            const bookingDate = row.BookingDATE ? parseDate(row.BookingDATE) : new Date();
            
            if (!dateInpute) {
                throw new Error('Date de début invalide');
            }
            
            const workingDaysResult = calculateWorkingDays(dateInpute, bookingDate);

            processedData.push({
                index: index + 1,
                dateInpute: formatDate(dateInpute),
                bookingDate: formatDate(bookingDate),
                workingDaysExact: workingDaysResult.exact,
                workingDaysRounded: workingDaysResult.rounded,
                status: row.BookingDATE ? 'Terminé' : 'En cours'
            });

            totalDays += workingDaysResult.exact;
            validRows++;
            minDays = Math.min(minDays, workingDaysResult.exact);
            maxDays = Math.max(maxDays, workingDaysResult.exact);
            
        } catch (error) {
            processedData.push({
                index: index + 1,
                dateInpute: row.dateInpute || 'N/A',
                bookingDate: row.BookingDATE || 'N/A',
                workingDaysExact: 'Erreur',
                workingDaysRounded: 'Erreur',
                status: 'Erreur: ' + error.message
            });
        }
    });
    
    // Mettre à jour les statistiques
    updateStats(validRows, totalDays / validRows, minDays, maxDays);
    
    // Afficher les résultats
    displayResults();
    
    showLoading(false);
}

function parseDate(dateStr) {
    if (!dateStr) return null;
    
    // Formats supportés: DD/MM/YYYY HH:MM, DD/MM/YYYY, YYYY-MM-DD
    const formats = [
        /^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{2})$/,  // DD/MM/YYYY HH:MM
        /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,                      // DD/MM/YYYY
        /^(\d{4})-(\d{1,2})-(\d{1,2})$/                         // YYYY-MM-DD
    ];
    
    for (let format of formats) {
        const match = dateStr.toString().match(format);
        if (match) {
            if (format === formats[2]) { // YYYY-MM-DD
                return new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));
            } else { // DD/MM/YYYY formats
                const day = parseInt(match[1]);
                const month = parseInt(match[2]) - 1;
                const year = parseInt(match[3]);
                const hour = match[4] ? parseInt(match[4]) : 0;
                const minute = match[5] ? parseInt(match[5]) : 0;
                return new Date(year, month, day, hour, minute);
            }
        }
    }
    
    return null;
}

function formatDate(date) {
    return date.toLocaleDateString('fr-FR') + ' ' + date.toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'});
}

function calculateWorkingDays(startDate, endDate) {
    if (startDate > endDate) {
        return { exact: 0, rounded: 0 };
    }

    // Calculer la différence totale en heures
    const totalHours = (endDate - startDate) / (1000 * 60 * 60);
    const totalDays = totalHours / 24;

    // Compter les jours ouvrés complets
    let workingDays = 0;
    let currentDate = new Date(startDate);
    currentDate.setDate(currentDate.getDate() + 1);

    while (currentDate <= endDate) {
        if (isWorkingDay(currentDate)) {
            workingDays++;
        }
        currentDate.setDate(currentDate.getDate() + 1);
    }

    // Calcul précis avec les heures
    let exactWorkingDays = calculateExactWorkingDays(startDate, endDate);

    // Arrondi selon les règles spécifiées
    let roundedDays = Math.round(exactWorkingDays);

    return {
        exact: Math.round(exactWorkingDays * 100) / 100, // 2 décimales
        rounded: roundedDays
    };
}

function calculateExactWorkingDays(startDate, endDate) {
    if (startDate > endDate) {
        return 0;
    }

    // Calculer la différence totale en heures
    const totalHours = (endDate - startDate) / (1000 * 60 * 60);
    const totalCalendarDays = totalHours / 24;

    // Compter les jours ouvrés complets entre les dates
    let workingDaysCount = 0;
    let currentDate = new Date(startDate);
    currentDate.setDate(currentDate.getDate() + 1);
    currentDate.setHours(0, 0, 0, 0);

    let endDateOnly = new Date(endDate);
    endDateOnly.setHours(0, 0, 0, 0);

    while (currentDate <= endDateOnly) {
        if (isWorkingDay(currentDate)) {
            workingDaysCount++;
        }
        currentDate.setDate(currentDate.getDate() + 1);
    }

    // Calculer la proportion basée sur la durée totale
    // Si la durée totale est inférieure à 1 jour, on calcule la proportion
    if (totalCalendarDays < 1) {
        // Vérifier si c'est un jour ouvré
        if (isWorkingDay(startDate)) {
            return totalCalendarDays;
        } else {
            return 0;
        }
    }

    // Pour les périodes plus longues, on utilise une approche proportionnelle
    // basée sur le ratio jours ouvrés / jours calendaires
    const totalCalendarDaysInt = Math.ceil(totalCalendarDays);
    let totalCalendarWorkingDays = 0;

    currentDate = new Date(startDate);
    for (let i = 0; i < totalCalendarDaysInt; i++) {
        if (isWorkingDay(currentDate)) {
            totalCalendarWorkingDays++;
        }
        currentDate.setDate(currentDate.getDate() + 1);
    }

    if (totalCalendarWorkingDays === 0) {
        return 0;
    }

    // Calculer le ratio et l'appliquer à la durée réelle
    const workingDayRatio = totalCalendarWorkingDays / totalCalendarDaysInt;
    return totalCalendarDays * workingDayRatio;
}

function isWorkingDay(date) {
    // Vérifier si c'est un week-end (samedi = 6, dimanche = 0)
    const dayOfWeek = date.getDay();
    if (dayOfWeek === 0 || dayOfWeek === 6) {
        return false;
    }
    
    // Vérifier si c'est un jour férié
    const dateStr = date.getFullYear() + '-' + 
                   String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                   String(date.getDate()).padStart(2, '0');
    
    return !holidays2025.includes(dateStr);
}

function updateStats(totalRows, avgDays, minDays, maxDays) {
    document.getElementById('totalRows').textContent = totalRows;
    document.getElementById('avgDays').textContent = isNaN(avgDays) ? '0' : Math.round(avgDays * 10) / 10;
    document.getElementById('minDays').textContent = minDays === Infinity ? '0' : minDays;
    document.getElementById('maxDays').textContent = maxDays === -Infinity ? '0' : maxDays;
    
    document.getElementById('statsSection').style.display = 'block';
    document.getElementById('statsSection').classList.add('fade-in');
}

function displayResults() {
    const tbody = document.getElementById('resultsBody');
    tbody.innerHTML = '';

    processedData.forEach(row => {
        const tr = document.createElement('tr');
        const exactClass = typeof row.workingDaysExact === 'number' ? 'text-info fw-bold' : 'text-danger';
        const roundedClass = typeof row.workingDaysRounded === 'number' ? 'text-success fw-bold' : 'text-danger';

        tr.innerHTML = `
            <td>${row.index}</td>
            <td>${row.dateInpute}</td>
            <td>${row.bookingDate}</td>
            <td class="${exactClass}">${row.workingDaysExact}${typeof row.workingDaysExact === 'number' ? ' j' : ''}</td>
            <td class="${roundedClass}">${row.workingDaysRounded}${typeof row.workingDaysRounded === 'number' ? ' j' : ''}</td>
            <td><span class="badge ${row.status.includes('Erreur') ? 'bg-danger' : row.status === 'En cours' ? 'bg-warning' : 'bg-success'}">${row.status}</span></td>
        `;
        tbody.appendChild(tr);
    });

    document.getElementById('resultsSection').style.display = 'block';
    document.getElementById('resultsSection').classList.add('fade-in');
}

function exportToExcel() {
    const exportData = processedData.map(row => ({
        'Numéro': row.index,
        'Date Début': row.dateInpute,
        'Date Fin': row.bookingDate,
        'Délai Exact (j)': row.workingDaysExact,
        'Délai Arrondi (j)': row.workingDaysRounded,
        'Statut': row.status
    }));

    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Délais Calculés');

    // Ajuster la largeur des colonnes
    const colWidths = [
        { wch: 10 },  // Numéro
        { wch: 20 },  // Date Début
        { wch: 20 },  // Date Fin
        { wch: 15 },  // Délai Exact
        { wch: 15 },  // Délai Arrondi
        { wch: 15 }   // Statut
    ];
    ws['!cols'] = colWidths;

    const fileName = `delais_calcules_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(wb, fileName);
}

function showLoading(show) {
    document.getElementById('loadingDiv').style.display = show ? 'block' : 'none';
}

function calculateTestDates() {
    const startDateInput = document.getElementById('testStartDate');
    const endDateInput = document.getElementById('testEndDate');
    const startTextInput = document.getElementById('testStartDateText');
    const endTextInput = document.getElementById('testEndDateText');
    const useCurrentDateCheckbox = document.getElementById('useCurrentDate');
    const testResult = document.getElementById('testResult');
    const testResultValue = document.getElementById('testResultValue');
    const testDetails = document.getElementById('testDetails');
    const testDetailsContent = document.getElementById('testDetailsContent');

    try {
        // Récupérer les dates selon le mode actif
        let startDate, endDate;

        // Date de début
        if (startTextInput.style.display !== 'none') {
            // Mode texte actif
            startDate = parseDate(startTextInput.value);
        } else {
            // Mode sélecteur actif
            startDate = new Date(startDateInput.value);
        }

        // Date de fin
        if (useCurrentDateCheckbox.checked) {
            endDate = new Date();
        } else {
            if (endTextInput.style.display !== 'none') {
                // Mode texte actif
                endDate = parseDate(endTextInput.value);
            } else {
                // Mode sélecteur actif
                endDate = new Date(endDateInput.value);
            }
        }

        // Vérifier la validité des dates
        if (isNaN(startDate.getTime())) {
            throw new Error('Date de début invalide');
        }
        if (isNaN(endDate.getTime())) {
            throw new Error('Date de fin invalide');
        }
        if (startDate > endDate) {
            throw new Error('La date de début doit être antérieure à la date de fin');
        }

        // Calculer les jours ouvrés
        const workingDaysResult = calculateWorkingDays(startDate, endDate);

        // Générer le détail du calcul
        const details = generateCalculationDetails(startDate, endDate, workingDaysResult);

        // Afficher le résultat
        testResultValue.innerHTML = `
            <div class="d-flex gap-2 flex-wrap">
                <span class="badge bg-info fs-6">Exact: ${workingDaysResult.exact} j</span>
                <span class="badge bg-success fs-6">Arrondi: ${workingDaysResult.rounded} j</span>
            </div>
        `;
        testResult.className = 'alert alert-success';
        testResult.style.display = 'block';

        // Afficher les détails
        testDetailsContent.innerHTML = details;
        testDetails.style.display = 'block';

        // Animation
        testResult.classList.add('fade-in');
        testDetails.classList.add('fade-in');

    } catch (error) {
        testResultValue.innerHTML = `<span class="badge bg-danger">${error.message}</span>`;
        testResult.className = 'alert alert-danger';
        testResult.style.display = 'block';
        testDetails.style.display = 'none';
    }
}

function generateCalculationDetails(startDate, endDate, workingDaysResult) {
    // Calcul de la durée totale
    const totalHours = (endDate - startDate) / (1000 * 60 * 60);
    const totalDays = totalHours / 24;

    let details = `<strong>Période analysée :</strong> ${formatDate(startDate)} → ${formatDate(endDate)}<br>`;
    details += `<strong>Durée totale :</strong> ${Math.round(totalHours * 100) / 100} heures (${Math.round(totalDays * 100) / 100} jours)<br><br>`;

    details += `<strong>Calcul des délais :</strong><br>`;
    details += `🔢 <strong>Délai exact :</strong> ${workingDaysResult.exact} jours<br>`;
    details += `🔄 <strong>Délai arrondi :</strong> ${workingDaysResult.rounded} jours<br><br>`;

    details += `<strong>Analyse jour par jour :</strong><br>`;

    let currentDate = new Date(startDate);
    currentDate.setDate(currentDate.getDate() + 1); // Commencer le lendemain

    let dayCount = 0;
    let workingDayCount = 0;

    while (currentDate <= endDate && dayCount < 50) { // Limiter l'affichage à 50 jours
        const dayName = currentDate.toLocaleDateString('fr-FR', { weekday: 'long' });
        const dateStr = currentDate.toLocaleDateString('fr-FR');
        const isWorking = isWorkingDay(currentDate);

        if (isWorking) {
            workingDayCount++;
            details += `📅 ${dateStr} (${dayName}) - <span class="text-success">✅ Jour ouvré</span><br>`;
        } else {
            const reason = getNotWorkingReason(currentDate);
            details += `📅 ${dateStr} (${dayName}) - <span class="text-danger">❌ ${reason}</span><br>`;
        }

        currentDate.setDate(currentDate.getDate() + 1);
        dayCount++;
    }

    if (currentDate <= endDate) {
        details += `<br><em>... et ${Math.ceil((endDate - currentDate) / (1000 * 60 * 60 * 24))} jour(s) supplémentaire(s)</em><br>`;
    }

    details += `<br><strong>Résumé :</strong><br>`;
    details += `• Jours ouvrés complets : ${workingDayCount}<br>`;
    details += `• Calcul avec heures : ${workingDaysResult.exact} jours<br>`;
    details += `• Arrondi final : ${workingDaysResult.rounded} jours`;

    return details;
}

function getNotWorkingReason(date) {
    const dayOfWeek = date.getDay();
    if (dayOfWeek === 0) return 'Dimanche';
    if (dayOfWeek === 6) return 'Samedi';

    const dateStr = date.getFullYear() + '-' +
                   String(date.getMonth() + 1).padStart(2, '0') + '-' +
                   String(date.getDate()).padStart(2, '0');

    const holidayNames = {
        "2025-01-01": "Jour de l'an",
        "2025-03-31": "Lundi de Pâques",
        "2025-04-04": "Fête de l'Indépendance",
        "2025-05-01": "Fête du Travail",
        "2025-05-29": "Ascension",
        "2025-06-07": "Aïd al-Adha",
        "2025-06-09": "Lundi de Pentecôte",
        "2025-06-26": "Ashura",
        "2025-08-15": "Assomption",
        "2025-09-05": "Mouloud",
        "2025-11-01": "Toussaint",
        "2025-12-25": "Noël"
    };

    return holidayNames[dateStr] || 'Jour férié';
}

function setQuickDate(type, period) {
    const now = new Date();
    let targetDate = new Date();

    switch (period) {
        case 'today':
            targetDate = now;
            break;
        case 'yesterday':
            targetDate.setDate(now.getDate() - 1);
            break;
        case 'tomorrow':
            targetDate.setDate(now.getDate() + 1);
            break;
        case 'week':
            if (type === 'start') {
                targetDate.setDate(now.getDate() - 7);
            } else {
                targetDate.setDate(now.getDate() + 7);
            }
            break;
    }

    // Mettre à jour les deux types de champs
    const formattedDateInput = formatDateForInput(targetDate);
    const formattedTextInput = formatDateForText(targetDate);

    if (type === 'start') {
        document.getElementById('testStartDate').value = formattedDateInput;
        document.getElementById('testStartDateText').value = formattedTextInput;

        // Valider le champ texte
        const textInput = document.getElementById('testStartDateText');
        const dateInput = document.getElementById('testStartDate');
        validateAndSyncDateInput(textInput, dateInput, 'start');
    } else {
        document.getElementById('testEndDate').value = formattedDateInput;
        document.getElementById('testEndDateText').value = formattedTextInput;

        // Valider le champ texte
        const textInput = document.getElementById('testEndDateText');
        const dateInput = document.getElementById('testEndDate');
        validateAndSyncDateInput(textInput, dateInput, 'end');
    }
}

function loadExample(exampleNumber) {
    const startDateInput = document.getElementById('testStartDate');
    const endDateInput = document.getElementById('testEndDate');
    const startTextInput = document.getElementById('testStartDateText');
    const endTextInput = document.getElementById('testEndDateText');
    const useCurrentDateCheckbox = document.getElementById('useCurrentDate');

    // Décocher la case "date actuelle"
    useCurrentDateCheckbox.checked = false;
    endDateInput.disabled = false;
    endTextInput.disabled = false;
    endDateInput.style.opacity = '1';
    endTextInput.style.opacity = '1';

    let startDateTime, endDateTime, startText, endText;

    switch (exampleNumber) {
        case 1:
            // Exemple avec le 1er mai férié
            startDateTime = '2025-04-29T09:47';
            endDateTime = '2025-05-06T15:44';
            startText = '29/04/2025 09:47';
            endText = '06/05/2025 15:44';
            break;
        case 2:
            // Exemple avec week-end
            startDateTime = '2025-05-03T10:00';
            endDateTime = '2025-05-05T15:00';
            startText = '03/05/2025 10:00';
            endText = '05/05/2025 15:00';
            break;
        case 3:
            // Exemple spécifique : 02/06 16:57 → 05/06 11:47
            startDateTime = '2025-06-02T16:57';
            endDateTime = '2025-06-05T11:47';
            startText = '02/06/2025 16:57';
            endText = '05/06/2025 11:47';
            break;
    }

    // Mettre à jour les deux types de champs
    startDateInput.value = startDateTime;
    endDateInput.value = endDateTime;
    startTextInput.value = startText;
    endTextInput.value = endText;

    // Valider les champs texte
    validateAndSyncDateInput(startTextInput, startDateInput, 'start');
    validateAndSyncDateInput(endTextInput, endDateInput, 'end');

    // Calculer automatiquement
    setTimeout(() => {
        calculateTestDates();
    }, 100);
}

function validateAndSyncDateInput(textInput, dateTimeInput, type) {
    const dateText = textInput.value.trim();

    // Supprimer les classes de validation précédentes
    textInput.classList.remove('is-valid', 'is-invalid');

    // Supprimer le feedback précédent
    const existingFeedback = textInput.parentNode.parentNode.querySelector('.date-validation-feedback');
    if (existingFeedback) {
        existingFeedback.remove();
    }

    if (!dateText) {
        return;
    }

    // Tenter de parser la date
    const parsedDate = parseDate(dateText);

    if (parsedDate && !isNaN(parsedDate.getTime())) {
        // Date valide
        textInput.classList.add('is-valid');

        // Synchroniser avec le datetime-local
        const formattedDate = formatDateForInput(parsedDate);
        dateTimeInput.value = formattedDate;

        // Ajouter feedback positif
        const feedback = document.createElement('div');
        feedback.className = 'date-validation-feedback text-success';
        feedback.innerHTML = `<i class="fas fa-check-circle"></i> ${formatDate(parsedDate)}`;
        textInput.parentNode.parentNode.appendChild(feedback);

    } else {
        // Date invalide
        textInput.classList.add('is-invalid');

        // Ajouter feedback négatif
        const feedback = document.createElement('div');
        feedback.className = 'date-validation-feedback text-danger';
        feedback.innerHTML = `<i class="fas fa-exclamation-circle"></i> Format invalide. Utilisez DD/MM/YYYY HH:MM`;
        textInput.parentNode.parentNode.appendChild(feedback);
    }
}

function syncDateTimeToText(dateTimeInput, textInput) {
    if (dateTimeInput.value) {
        const date = new Date(dateTimeInput.value);
        const formattedText = formatDateForText(date);
        textInput.value = formattedText;

        // Valider le champ texte
        const type = textInput.id.includes('Start') ? 'start' : 'end';
        validateAndSyncDateInput(textInput, dateTimeInput, type);
    }
}

function formatDateForInput(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}`;
}

function formatDateForText(date) {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${day}/${month}/${year} ${hours}:${minutes}`;
}

function toggleDateInputMode(type) {
    const textInput = document.getElementById(`test${type === 'start' ? 'Start' : 'End'}DateText`);
    const dateTimeInput = document.getElementById(`test${type === 'start' ? 'Start' : 'End'}Date`);
    const toggleBtn = document.getElementById(`toggle${type === 'start' ? 'Start' : 'End'}Picker`);

    if (textInput.style.display === 'none') {
        // Basculer vers mode texte
        textInput.style.display = 'block';
        dateTimeInput.style.display = 'none';
        toggleBtn.innerHTML = '<i class="fas fa-calendar-alt"></i>';
        toggleBtn.title = 'Basculer vers sélecteur';

        // Synchroniser la valeur
        syncDateTimeToText(dateTimeInput, textInput);
    } else {
        // Basculer vers mode sélecteur
        textInput.style.display = 'none';
        dateTimeInput.style.display = 'block';
        toggleBtn.innerHTML = '<i class="fas fa-keyboard"></i>';
        toggleBtn.title = 'Basculer vers saisie texte';

        // Synchroniser la valeur si le texte est valide
        const parsedDate = parseDate(textInput.value);
        if (parsedDate && !isNaN(parsedDate.getTime())) {
            dateTimeInput.value = formatDateForInput(parsedDate);
        }
    }
}

// Fonctions globales pour les boutons
window.setQuickDate = setQuickDate;
window.loadExample = loadExample;
