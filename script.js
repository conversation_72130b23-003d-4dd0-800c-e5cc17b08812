// Liste des jours fériés au Sénégal pour 2025
const holidays2025 = [
    "2025-01-01",  // <PERSON><PERSON> <PERSON>'<PERSON>
    "2025-03-31",  // <PERSON><PERSON> (Estimation, Aïd al-Fitr/Korité)
    "2025-04-04",  // Fête de l'Indépendance
    "2025-05-01",  // Fête du Travail
    "2025-05-29",  // Ascension
    "2025-06-07",  // <PERSON><PERSON><PERSON> (Tabaski)
    "2025-06-09",  // Lundi de Pentecôte
    "2025-06-26",  // Ashura (Tamkharit)
    "2025-08-15",  // Assomption
    "2025-09-05",  // Anniversaire du Prophète (Mouloud)
    "2025-11-01",  // Toussaint
    "2025-12-25",  // Noël
];

let processedData = [];

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
});

function setupEventListeners() {
    const uploadZone = document.getElementById('uploadZone');
    const fileInput = document.getElementById('fileInput');
    const exportBtn = document.getElementById('exportBtn');
    const calculateTestBtn = document.getElementById('calculateTestBtn');
    const useCurrentDate = document.getElementById('useCurrentDate');
    const testEndDate = document.getElementById('testEndDate');

    // Drag & Drop
    uploadZone.addEventListener('dragover', handleDragOver);
    uploadZone.addEventListener('dragleave', handleDragLeave);
    uploadZone.addEventListener('drop', handleDrop);
    uploadZone.addEventListener('click', () => fileInput.click());

    // File input
    fileInput.addEventListener('change', handleFileSelect);

    // Export button
    exportBtn.addEventListener('click', exportToExcel);

    // Test calculator
    calculateTestBtn.addEventListener('click', calculateTestDates);

    // Checkbox pour date actuelle
    useCurrentDate.addEventListener('change', function() {
        testEndDate.disabled = this.checked;
        if (this.checked) {
            testEndDate.style.opacity = '0.5';
        } else {
            testEndDate.style.opacity = '1';
        }
    });
}

function handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('dragover');
}

function handleDragLeave(e) {
    e.currentTarget.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        processFile(files[0]);
    }
}

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        processFile(file);
    }
}

function processFile(file) {
    showLoading(true);
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            let data;
            const fileName = file.name.toLowerCase();
            
            if (fileName.endsWith('.csv')) {
                data = parseCSV(e.target.result);
            } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
                data = parseExcel(e.target.result);
            } else {
                throw new Error('Format de fichier non supporté');
            }
            
            processData(data);
        } catch (error) {
            alert('Erreur lors du traitement du fichier: ' + error.message);
            showLoading(false);
        }
    };
    
    if (file.name.toLowerCase().endsWith('.csv')) {
        reader.readAsText(file);
    } else {
        reader.readAsArrayBuffer(file);
    }
}

function parseCSV(text) {
    const lines = text.split('\n');
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const data = [];
    
    for (let i = 1; i < lines.length; i++) {
        if (lines[i].trim()) {
            const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
            const row = {};
            headers.forEach((header, index) => {
                row[header] = values[index] || '';
            });
            data.push(row);
        }
    }
    
    return data;
}

function parseExcel(buffer) {
    const workbook = XLSX.read(buffer, { type: 'array' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    return XLSX.utils.sheet_to_json(worksheet);
}

function processData(data) {
    processedData = [];
    let totalDays = 0;
    let validRows = 0;
    let minDays = Infinity;
    let maxDays = -Infinity;
    
    data.forEach((row, index) => {
        try {
            const dateInpute = parseDate(row.dateInpute);
            const bookingDate = row.BookingDATE ? parseDate(row.BookingDATE) : new Date();
            
            if (!dateInpute) {
                throw new Error('Date de début invalide');
            }
            
            const workingDays = calculateWorkingDays(dateInpute, bookingDate);
            
            processedData.push({
                index: index + 1,
                dateInpute: formatDate(dateInpute),
                bookingDate: formatDate(bookingDate),
                workingDays: workingDays,
                status: row.BookingDATE ? 'Terminé' : 'En cours'
            });
            
            totalDays += workingDays;
            validRows++;
            minDays = Math.min(minDays, workingDays);
            maxDays = Math.max(maxDays, workingDays);
            
        } catch (error) {
            processedData.push({
                index: index + 1,
                dateInpute: row.dateInpute || 'N/A',
                bookingDate: row.BookingDATE || 'N/A',
                workingDays: 'Erreur',
                status: 'Erreur: ' + error.message
            });
        }
    });
    
    // Mettre à jour les statistiques
    updateStats(validRows, totalDays / validRows, minDays, maxDays);
    
    // Afficher les résultats
    displayResults();
    
    showLoading(false);
}

function parseDate(dateStr) {
    if (!dateStr) return null;
    
    // Formats supportés: DD/MM/YYYY HH:MM, DD/MM/YYYY, YYYY-MM-DD
    const formats = [
        /^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{2})$/,  // DD/MM/YYYY HH:MM
        /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,                      // DD/MM/YYYY
        /^(\d{4})-(\d{1,2})-(\d{1,2})$/                         // YYYY-MM-DD
    ];
    
    for (let format of formats) {
        const match = dateStr.toString().match(format);
        if (match) {
            if (format === formats[2]) { // YYYY-MM-DD
                return new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));
            } else { // DD/MM/YYYY formats
                const day = parseInt(match[1]);
                const month = parseInt(match[2]) - 1;
                const year = parseInt(match[3]);
                const hour = match[4] ? parseInt(match[4]) : 0;
                const minute = match[5] ? parseInt(match[5]) : 0;
                return new Date(year, month, day, hour, minute);
            }
        }
    }
    
    return null;
}

function formatDate(date) {
    return date.toLocaleDateString('fr-FR') + ' ' + date.toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'});
}

function calculateWorkingDays(startDate, endDate) {
    if (startDate > endDate) {
        return 0;
    }

    let workingDays = 0;
    let currentDate = new Date(startDate);

    // Ne pas compter le premier jour, commencer le lendemain
    currentDate.setDate(currentDate.getDate() + 1);

    while (currentDate <= endDate) {
        if (isWorkingDay(currentDate)) {
            workingDays++;
        }
        currentDate.setDate(currentDate.getDate() + 1);
    }

    return workingDays;
}

function isWorkingDay(date) {
    // Vérifier si c'est un week-end (samedi = 6, dimanche = 0)
    const dayOfWeek = date.getDay();
    if (dayOfWeek === 0 || dayOfWeek === 6) {
        return false;
    }
    
    // Vérifier si c'est un jour férié
    const dateStr = date.getFullYear() + '-' + 
                   String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                   String(date.getDate()).padStart(2, '0');
    
    return !holidays2025.includes(dateStr);
}

function updateStats(totalRows, avgDays, minDays, maxDays) {
    document.getElementById('totalRows').textContent = totalRows;
    document.getElementById('avgDays').textContent = isNaN(avgDays) ? '0' : Math.round(avgDays * 10) / 10;
    document.getElementById('minDays').textContent = minDays === Infinity ? '0' : minDays;
    document.getElementById('maxDays').textContent = maxDays === -Infinity ? '0' : maxDays;
    
    document.getElementById('statsSection').style.display = 'block';
    document.getElementById('statsSection').classList.add('fade-in');
}

function displayResults() {
    const tbody = document.getElementById('resultsBody');
    tbody.innerHTML = '';
    
    processedData.forEach(row => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${row.index}</td>
            <td>${row.dateInpute}</td>
            <td>${row.bookingDate}</td>
            <td class="${typeof row.workingDays === 'number' ? 'text-success fw-bold' : 'text-danger'}">${row.workingDays}</td>
            <td><span class="badge ${row.status.includes('Erreur') ? 'bg-danger' : row.status === 'En cours' ? 'bg-warning' : 'bg-success'}">${row.status}</span></td>
        `;
        tbody.appendChild(tr);
    });
    
    document.getElementById('resultsSection').style.display = 'block';
    document.getElementById('resultsSection').classList.add('fade-in');
}

function exportToExcel() {
    const exportData = processedData.map(row => ({
        'Numéro': row.index,
        'Date Début': row.dateInpute,
        'Date Fin': row.bookingDate,
        'Jours Ouvrés': row.workingDays,
        'Statut': row.status
    }));
    
    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Délais Calculés');
    
    // Ajuster la largeur des colonnes
    const colWidths = [
        { wch: 10 },  // Numéro
        { wch: 20 },  // Date Début
        { wch: 20 },  // Date Fin
        { wch: 15 },  // Jours Ouvrés
        { wch: 15 }   // Statut
    ];
    ws['!cols'] = colWidths;
    
    const fileName = `delais_calcules_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(wb, fileName);
}

function showLoading(show) {
    document.getElementById('loadingDiv').style.display = show ? 'block' : 'none';
}

function calculateTestDates() {
    const startDateInput = document.getElementById('testStartDate');
    const endDateInput = document.getElementById('testEndDate');
    const useCurrentDateCheckbox = document.getElementById('useCurrentDate');
    const testResult = document.getElementById('testResult');
    const testResultValue = document.getElementById('testResultValue');
    const testDetails = document.getElementById('testDetails');
    const testDetailsContent = document.getElementById('testDetailsContent');

    try {
        // Récupérer les dates
        const startDate = new Date(startDateInput.value);
        const endDate = useCurrentDateCheckbox.checked ? new Date() : new Date(endDateInput.value);

        // Vérifier la validité des dates
        if (isNaN(startDate.getTime())) {
            throw new Error('Date de début invalide');
        }
        if (isNaN(endDate.getTime())) {
            throw new Error('Date de fin invalide');
        }
        if (startDate > endDate) {
            throw new Error('La date de début doit être antérieure à la date de fin');
        }

        // Calculer les jours ouvrés
        const workingDays = calculateWorkingDays(startDate, endDate);

        // Générer le détail du calcul
        const details = generateCalculationDetails(startDate, endDate);

        // Afficher le résultat
        testResultValue.innerHTML = `
            <span class="badge bg-success fs-6">${workingDays} jour${workingDays > 1 ? 's' : ''} ouvré${workingDays > 1 ? 's' : ''}</span>
        `;
        testResult.className = 'alert alert-success';
        testResult.style.display = 'block';

        // Afficher les détails
        testDetailsContent.innerHTML = details;
        testDetails.style.display = 'block';

        // Animation
        testResult.classList.add('fade-in');
        testDetails.classList.add('fade-in');

    } catch (error) {
        testResultValue.innerHTML = `<span class="badge bg-danger">${error.message}</span>`;
        testResult.className = 'alert alert-danger';
        testResult.style.display = 'block';
        testDetails.style.display = 'none';
    }
}

function generateCalculationDetails(startDate, endDate) {
    let details = `<strong>Période analysée :</strong> ${formatDate(startDate)} → ${formatDate(endDate)}<br><br>`;
    details += `<strong>Analyse jour par jour :</strong><br>`;

    let currentDate = new Date(startDate);
    currentDate.setDate(currentDate.getDate() + 1); // Commencer le lendemain

    let dayCount = 0;
    let workingDayCount = 0;

    while (currentDate <= endDate && dayCount < 50) { // Limiter l'affichage à 50 jours
        const dayName = currentDate.toLocaleDateString('fr-FR', { weekday: 'long' });
        const dateStr = currentDate.toLocaleDateString('fr-FR');
        const isWorking = isWorkingDay(currentDate);

        if (isWorking) {
            workingDayCount++;
            details += `📅 ${dateStr} (${dayName}) - <span class="text-success">✅ Jour ouvré</span><br>`;
        } else {
            const reason = getNotWorkingReason(currentDate);
            details += `📅 ${dateStr} (${dayName}) - <span class="text-danger">❌ ${reason}</span><br>`;
        }

        currentDate.setDate(currentDate.getDate() + 1);
        dayCount++;
    }

    if (currentDate <= endDate) {
        details += `<br><em>... et ${Math.ceil((endDate - currentDate) / (1000 * 60 * 60 * 24))} jour(s) supplémentaire(s)</em><br>`;
    }

    details += `<br><strong>Total :</strong> ${workingDayCount} jour${workingDayCount > 1 ? 's' : ''} ouvré${workingDayCount > 1 ? 's' : ''}`;

    return details;
}

function getNotWorkingReason(date) {
    const dayOfWeek = date.getDay();
    if (dayOfWeek === 0) return 'Dimanche';
    if (dayOfWeek === 6) return 'Samedi';

    const dateStr = date.getFullYear() + '-' +
                   String(date.getMonth() + 1).padStart(2, '0') + '-' +
                   String(date.getDate()).padStart(2, '0');

    const holidayNames = {
        "2025-01-01": "Jour de l'an",
        "2025-03-31": "Lundi de Pâques",
        "2025-04-04": "Fête de l'Indépendance",
        "2025-05-01": "Fête du Travail",
        "2025-05-29": "Ascension",
        "2025-06-07": "Aïd al-Adha",
        "2025-06-09": "Lundi de Pentecôte",
        "2025-06-26": "Ashura",
        "2025-08-15": "Assomption",
        "2025-09-05": "Mouloud",
        "2025-11-01": "Toussaint",
        "2025-12-25": "Noël"
    };

    return holidayNames[dateStr] || 'Jour férié';
}

function setQuickDate(type, period) {
    const now = new Date();
    let targetDate = new Date();

    switch (period) {
        case 'today':
            targetDate = now;
            break;
        case 'yesterday':
            targetDate.setDate(now.getDate() - 1);
            break;
        case 'tomorrow':
            targetDate.setDate(now.getDate() + 1);
            break;
        case 'week':
            if (type === 'start') {
                targetDate.setDate(now.getDate() - 7);
            } else {
                targetDate.setDate(now.getDate() + 7);
            }
            break;
    }

    // Formater la date pour l'input datetime-local
    const year = targetDate.getFullYear();
    const month = String(targetDate.getMonth() + 1).padStart(2, '0');
    const day = String(targetDate.getDate()).padStart(2, '0');
    const hours = String(targetDate.getHours()).padStart(2, '0');
    const minutes = String(targetDate.getMinutes()).padStart(2, '0');

    const formattedDate = `${year}-${month}-${day}T${hours}:${minutes}`;

    if (type === 'start') {
        document.getElementById('testStartDate').value = formattedDate;
    } else {
        document.getElementById('testEndDate').value = formattedDate;
    }
}

function loadExample(exampleNumber) {
    const startDateInput = document.getElementById('testStartDate');
    const endDateInput = document.getElementById('testEndDate');
    const useCurrentDateCheckbox = document.getElementById('useCurrentDate');

    // Décocher la case "date actuelle"
    useCurrentDateCheckbox.checked = false;
    endDateInput.disabled = false;
    endDateInput.style.opacity = '1';

    switch (exampleNumber) {
        case 1:
            // Exemple avec le 1er mai férié
            startDateInput.value = '2025-04-29T09:47';
            endDateInput.value = '2025-05-06T15:44';
            break;
        case 2:
            // Exemple avec week-end
            startDateInput.value = '2025-05-03T10:00';
            endDateInput.value = '2025-05-05T15:00';
            break;
        case 3:
            // Exemple avec Ascension (29/05)
            startDateInput.value = '2025-05-28T10:00';
            endDateInput.value = '2025-05-30T16:00';
            break;
    }

    // Calculer automatiquement
    setTimeout(() => {
        calculateTestDates();
    }, 100);
}

// Fonctions globales pour les boutons
window.setQuickDate = setQuickDate;
window.loadExample = loadExample;
