<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculateur de Jours Ouvrés - Sénégal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }
        
        .upload-zone {
            border: 3px dashed #3498db;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-zone:hover {
            border-color: #2980b9;
            background: #e3f2fd;
        }
        
        .upload-zone.dragover {
            border-color: #27ae60;
            background: #d5f4e6;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(52, 152, 219, 0.3);
            color: white;
        }
        
        .btn-success-custom {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        }
        
        .btn-success-custom:hover {
            box-shadow: 0 10px 20px rgba(39, 174, 96, 0.3);
        }
        
        .results-table {
            max-height: 400px;
            overflow-y: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .holiday-list {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin: 10px 0;
        }
        
        .loading {
            display: none;
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- En-tête -->
            <div class="header">
                <h1><i class="fas fa-calendar-alt"></i> Calculateur de Jours Ouvrés</h1>
                <p class="mb-0">Calcul automatique des délais en jours ouvrés (Sénégal)</p>
            </div>
            
            <div class="p-4">
                <!-- Zone d'upload -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h3><i class="fas fa-upload"></i> Importer les données</h3>
                        <div class="upload-zone" id="uploadZone">
                            <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                            <h4>Glissez-déposez votre fichier ici</h4>
                            <p class="text-muted">ou cliquez pour sélectionner un fichier CSV/Excel</p>
                            <input type="file" id="fileInput" accept=".csv,.xlsx,.xls" style="display: none;">
                            <button class="btn btn-custom mt-2" onclick="document.getElementById('fileInput').click()">
                                <i class="fas fa-folder-open"></i> Choisir un fichier
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Exemple de format -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5><i class="fas fa-info-circle"></i> Format attendu</h5>
                            </div>
                            <div class="card-body">
                                <p>Votre fichier doit contenir les colonnes :</p>
                                <ul>
                                    <li><strong>dateInpute</strong> : Date de début (DD/MM/YYYY HH:MM)</li>
                                    <li><strong>BookingDATE</strong> : Date de fin (DD/MM/YYYY HH:MM)</li>
                                </ul>
                                <small class="text-muted">Si BookingDATE est vide, la date d'aujourd'hui sera utilisée.</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="holiday-list">
                            <h5><i class="fas fa-calendar-times"></i> Jours fériés 2025 (Sénégal)</h5>
                            <div class="row">
                                <div class="col-6">
                                    <small>
                                        • 01/01 - Jour de l'an<br>
                                        • 31/03 - Lundi de Pâques<br>
                                        • 04/04 - Fête de l'Indépendance<br>
                                        • 01/05 - Fête du Travail<br>
                                        • 29/05 - Ascension<br>
                                        • 07/06 - Aïd al-Adha
                                    </small>
                                </div>
                                <div class="col-6">
                                    <small>
                                        • 09/06 - Lundi de Pentecôte<br>
                                        • 26/06 - Ashura<br>
                                        • 15/08 - Assomption<br>
                                        • 05/09 - Mouloud<br>
                                        • 01/11 - Toussaint<br>
                                        • 25/12 - Noël
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Statistiques -->
                <div class="row mb-4" id="statsSection" style="display: none;">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h3 id="totalRows">0</h3>
                            <p>Lignes traitées</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h3 id="avgDays">0</h3>
                            <p>Délai moyen (jours)</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h3 id="minDays">0</h3>
                            <p>Délai minimum</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h3 id="maxDays">0</h3>
                            <p>Délai maximum</p>
                        </div>
                    </div>
                </div>
                
                <!-- Résultats -->
                <div class="row" id="resultsSection" style="display: none;">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3><i class="fas fa-table"></i> Résultats</h3>
                            <button class="btn btn-success-custom btn-custom" id="exportBtn">
                                <i class="fas fa-download"></i> Exporter en Excel
                            </button>
                        </div>
                        
                        <div class="results-table">
                            <table class="table table-striped table-hover" id="resultsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>Date Début</th>
                                        <th>Date Fin</th>
                                        <th>Jours Ouvrés</th>
                                        <th>Statut</th>
                                    </tr>
                                </thead>
                                <tbody id="resultsBody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Loading -->
                <div class="text-center loading" id="loadingDiv">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2">Traitement en cours...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
