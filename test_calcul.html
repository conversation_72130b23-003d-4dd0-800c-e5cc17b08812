<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test de Calcul - Jours Ouvrés</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .result { font-weight: bold; color: #2c3e50; }
        .expected { color: #27ae60; }
        .actual { color: #e74c3c; }
        .match { color: #27ae60; }
    </style>
</head>
<body>
    <h1>🧪 Test de Calcul des Jours Ouvrés</h1>
    <div id="results"></div>

    <script>
        // Copie des fonctions de calcul depuis script.js
        const holidays2025 = [
            "2025-01-01", "2025-03-31", "2025-04-04", "2025-05-01", "2025-05-29",
            "2025-06-07", "2025-06-09", "2025-06-26", "2025-08-15", "2025-09-05",
            "2025-11-01", "2025-12-25"
        ];

        function parseDate(dateStr) {
            if (!dateStr) return null;
            const formats = [
                /^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{2})$/,
                /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,
                /^(\d{4})-(\d{1,2})-(\d{1,2})$/
            ];
            
            for (let format of formats) {
                const match = dateStr.toString().match(format);
                if (match) {
                    if (format === formats[2]) {
                        return new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));
                    } else {
                        const day = parseInt(match[1]);
                        const month = parseInt(match[2]) - 1;
                        const year = parseInt(match[3]);
                        const hour = match[4] ? parseInt(match[4]) : 0;
                        const minute = match[5] ? parseInt(match[5]) : 0;
                        return new Date(year, month, day, hour, minute);
                    }
                }
            }
            return null;
        }

        function isWorkingDay(date) {
            const dayOfWeek = date.getDay();
            if (dayOfWeek === 0 || dayOfWeek === 6) return false;
            
            const dateStr = date.getFullYear() + '-' + 
                           String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                           String(date.getDate()).padStart(2, '0');
            
            return !holidays2025.includes(dateStr);
        }

        function calculateWorkingDays(startDate, endDate) {
            if (startDate > endDate) return 0;
            
            let workingDays = 0;
            let currentDate = new Date(startDate);
            currentDate.setDate(currentDate.getDate() + 1);
            
            while (currentDate <= endDate) {
                if (isWorkingDay(currentDate)) {
                    workingDays++;
                }
                currentDate.setDate(currentDate.getDate() + 1);
            }
            
            return workingDays;
        }

        // Cas de test
        const testCases = [
            {
                name: "Cas 1: 29/04 à 06/05 (avec 1er mai férié)",
                start: "29/04/2025 09:47",
                end: "06/05/2025 15:44",
                expected: 5,
                description: "Du mardi 29/04 au mardi 06/05, excluant le 1er mai"
            },
            {
                name: "Cas 2: 29/04 à 02/05 (avec 1er mai férié)",
                start: "29/04/2025 10:53",
                end: "02/05/2025 13:27",
                expected: 2,
                description: "Du mardi 29/04 au vendredi 02/05, excluant le 1er mai"
            },
            {
                name: "Cas 3: Week-end simple",
                start: "03/05/2025 10:00",
                end: "05/05/2025 15:00",
                expected: 1,
                description: "Du samedi 03/05 au lundi 05/05, seul le lundi compte"
            },
            {
                name: "Cas 4: Même jour",
                start: "05/05/2025 09:00",
                end: "05/05/2025 17:00",
                expected: 0,
                description: "Même jour = 0 jours ouvrés"
            },
            {
                name: "Cas 5: Avec Ascension (29/05)",
                start: "28/05/2025 10:00",
                end: "30/05/2025 16:00",
                expected: 1,
                description: "Du mercredi 28/05 au vendredi 30/05, excluant l'Ascension"
            }
        ];

        // Exécution des tests
        function runTests() {
            const resultsDiv = document.getElementById('results');
            let allPassed = true;

            testCases.forEach((testCase, index) => {
                const startDate = parseDate(testCase.start);
                const endDate = parseDate(testCase.end);
                const actual = calculateWorkingDays(startDate, endDate);
                const passed = actual === testCase.expected;
                
                if (!passed) allPassed = false;

                const testDiv = document.createElement('div');
                testDiv.className = 'test-case';
                testDiv.innerHTML = `
                    <h3>${testCase.name} ${passed ? '✅' : '❌'}</h3>
                    <p><strong>Description:</strong> ${testCase.description}</p>
                    <p><strong>Période:</strong> ${testCase.start} → ${testCase.end}</p>
                    <p class="result">
                        <span class="expected">Attendu: ${testCase.expected} jours</span> | 
                        <span class="${passed ? 'match' : 'actual'}">Calculé: ${actual} jours</span>
                    </p>
                    ${!passed ? '<p style="color: red;">❌ ÉCHEC DU TEST</p>' : '<p style="color: green;">✅ TEST RÉUSSI</p>'}
                `;
                resultsDiv.appendChild(testDiv);
            });

            // Résumé
            const summaryDiv = document.createElement('div');
            summaryDiv.innerHTML = `
                <h2>${allPassed ? '🎉 Tous les tests sont réussis !' : '⚠️ Certains tests ont échoué'}</h2>
                <p>Tests exécutés: ${testCases.length}</p>
                <p>Tests réussis: ${testCases.filter((tc, i) => {
                    const startDate = parseDate(tc.start);
                    const endDate = parseDate(tc.end);
                    const actual = calculateWorkingDays(startDate, endDate);
                    return actual === tc.expected;
                }).length}</p>
            `;
            resultsDiv.appendChild(summaryDiv);
        }

        // Lancer les tests au chargement
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
