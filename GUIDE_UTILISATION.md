# 📖 Guide d'Utilisation - Calculateur de Jours Ouvrés

## 🚀 Démarrage Rapide

### 1. Ouvrir l'application
- Double-cliquez sur `index.html`
- L'application s'ouvre dans votre navigateur par défaut

### 2. Préparer vos données
Votre fichier doit contenir au minimum ces colonnes :
- **dateInpute** : Date de début
- **BookingDATE** : Date de fin (peut être vide)

### 3. Formats de date acceptés
- `DD/MM/YYYY HH:MM` (ex: 29/04/2025 09:47)
- `DD/MM/YYYY` (ex: 29/04/2025)
- `YYYY-MM-DD` (ex: 2025-04-29)

## 📋 Étapes détaillées

### Étape 1 : Import des données
1. **Glisser-déposer** : Faites glisser votre fichier CSV/Excel sur la zone bleue
2. **Ou cliquer** : C<PERSON>z sur "Choisir un fichier" et sélectionnez votre fichier

### Étape 2 : Traitement automatique
- L'application lit automatiquement votre fichier
- Parse les dates selon les formats supportés
- Calcule les jours ouvrés pour chaque ligne
- Affiche les statistiques en temps réel

### Étape 3 : Consultation des résultats
- **Statistiques** : Nombre total, délai moyen, min/max
- **Tableau détaillé** : Toutes les lignes avec résultats
- **Codes couleur** : Vert (succès), Orange (en cours), Rouge (erreur)

### Étape 4 : Export
- Cliquez sur "Exporter en Excel"
- Le fichier se télécharge automatiquement
- Nom : `delais_calcules_YYYY-MM-DD.xlsx`

## 🧮 Logique de Calcul

### Règles appliquées :
1. **Exclusion des week-ends** : Samedi et dimanche ne comptent pas
2. **Exclusion des jours fériés** : Liste complète des fêtes sénégalaises 2025
3. **Date de fin vide** : Utilise la date d'aujourd'hui
4. **Calcul inclusif** : Du jour suivant la date de début jusqu'à la date de fin

### Exemple de calcul :
```
Date début : 29/04/2025 (mardi)
Date fin   : 06/05/2025 (mardi)

Jours analysés :
- 30/04/2025 (mercredi) ✅ Jour ouvré
- 01/05/2025 (jeudi)    ❌ Fête du Travail
- 02/05/2025 (vendredi) ✅ Jour ouvré
- 03/05/2025 (samedi)   ❌ Week-end
- 04/05/2025 (dimanche) ❌ Week-end
- 05/05/2025 (lundi)    ✅ Jour ouvré
- 06/05/2025 (mardi)    ✅ Jour ouvré

Résultat : 4 jours ouvrés
```

## 📊 Interprétation des Résultats

### Colonnes du tableau :
- **#** : Numéro de ligne
- **Date Début** : Date de début formatée
- **Date Fin** : Date de fin formatée (ou date actuelle si vide)
- **Jours Ouvrés** : Nombre calculé (en vert si succès)
- **Statut** : 
  - 🟢 **Terminé** : Date de fin fournie
  - 🟡 **En cours** : Date de fin vide (utilise aujourd'hui)
  - 🔴 **Erreur** : Problème de format ou de données

### Statistiques :
- **Lignes traitées** : Nombre total de lignes valides
- **Délai moyen** : Moyenne des jours ouvrés calculés
- **Délai minimum** : Plus petit nombre de jours
- **Délai maximum** : Plus grand nombre de jours

## ⚠️ Gestion d'Erreurs

### Erreurs courantes et solutions :

#### 1. "Date de début invalide"
**Cause** : Format de date non reconnu
**Solution** : Utilisez DD/MM/YYYY HH:MM ou DD/MM/YYYY

#### 2. "Format de fichier non supporté"
**Cause** : Extension de fichier incorrecte
**Solution** : Utilisez .csv, .xlsx ou .xls

#### 3. Colonnes manquantes
**Cause** : Les colonnes dateInpute ou BookingDATE n'existent pas
**Solution** : Vérifiez les noms de colonnes (sensible à la casse)

#### 4. Dates incohérentes
**Cause** : Date de fin antérieure à la date de début
**Solution** : Vérifiez vos données source

## 📁 Fichiers Fournis

### Fichiers principaux :
- `index.html` : Application principale
- `script.js` : Logique de calcul
- `README.md` : Documentation complète

### Fichiers de test :
- `exemple_donnees.csv` : Données d'exemple pour tester
- `test_calcul.html` : Tests unitaires de la logique
- `GUIDE_UTILISATION.md` : Ce guide

## 🔧 Dépannage

### L'application ne se charge pas :
1. Vérifiez que vous utilisez un navigateur moderne
2. Assurez-vous d'avoir une connexion internet (pour les CDN)
3. Ouvrez la console développeur (F12) pour voir les erreurs

### Les calculs semblent incorrects :
1. Ouvrez `test_calcul.html` pour vérifier la logique
2. Vérifiez le format de vos dates
3. Consultez la liste des jours fériés

### L'export Excel ne fonctionne pas :
1. Vérifiez que votre navigateur autorise les téléchargements
2. Essayez avec un autre navigateur
3. Vérifiez l'espace disque disponible

## 📞 Support Technique

### Auto-diagnostic :
1. **Testez avec l'exemple** : Utilisez `exemple_donnees.csv`
2. **Vérifiez les tests** : Ouvrez `test_calcul.html`
3. **Consultez la console** : Appuyez sur F12 dans le navigateur

### Informations utiles pour le support :
- Version du navigateur
- Système d'exploitation
- Format et taille du fichier
- Message d'erreur exact
- Capture d'écran si possible

## 🎯 Conseils d'Utilisation

### Pour de meilleures performances :
- Limitez les fichiers à 10 000 lignes maximum
- Utilisez le format CSV pour les gros volumes
- Fermez les autres onglets du navigateur

### Pour éviter les erreurs :
- Vérifiez vos données avant l'import
- Utilisez des formats de date cohérents
- Évitez les cellules fusionnées dans Excel

### Pour des résultats optimaux :
- Nettoyez vos données (supprimez les lignes vides)
- Utilisez des noms de colonnes exacts
- Testez avec un petit échantillon d'abord

## 🔄 Mises à Jour

Cette application est en version 1.0. Les futures améliorations incluront :
- Support multi-années pour les jours fériés
- Personnalisation des jours fériés
- Graphiques et visualisations
- Mode batch pour plusieurs fichiers

---

**💡 Astuce** : Gardez ce guide ouvert pendant votre première utilisation pour une référence rapide !
