<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic CSV - Analyseur de Format</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .upload-zone { 
            border: 3px dashed #007bff; 
            padding: 40px; 
            text-align: center; 
            border-radius: 10px; 
            margin: 20px 0;
            cursor: pointer;
        }
        .upload-zone:hover { background: #f8f9fa; }
        .result { margin: 20px 0; padding: 15px; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f8f9fa; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Diagnostic CSV - Analyseur de Format</h1>
        
        <div class="upload-zone" id="uploadZone">
            <h3>📁 Glissez-déposez votre fichier CSV ici</h3>
            <p>ou cliquez pour sélectionner un fichier</p>
            <input type="file" id="fileInput" accept=".csv" style="display: none;">
            <button class="btn" onclick="document.getElementById('fileInput').click()">Choisir un fichier</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const uploadZone = document.getElementById('uploadZone');
        const fileInput = document.getElementById('fileInput');
        const results = document.getElementById('results');

        uploadZone.addEventListener('click', () => fileInput.click());
        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.style.background = '#e3f2fd';
        });
        uploadZone.addEventListener('dragleave', () => {
            uploadZone.style.background = '';
        });
        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.style.background = '';
            if (e.dataTransfer.files.length > 0) {
                analyzeFile(e.dataTransfer.files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                analyzeFile(e.target.files[0]);
            }
        });

        function analyzeFile(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const content = e.target.result;
                analyzeCSV(content, file.name);
            };
            reader.readAsText(file);
        }

        function analyzeCSV(content, fileName) {
            results.innerHTML = '';
            
            // Informations générales
            addResult('info', `📄 Fichier analysé : ${fileName}`);
            addResult('info', `📊 Taille : ${content.length} caractères`);
            
            const lines = content.split('\n').filter(line => line.trim());
            addResult('info', `📋 Nombre de lignes : ${lines.length}`);
            
            if (lines.length === 0) {
                addResult('error', '❌ Fichier vide');
                return;
            }
            
            // Analyse de la première ligne (en-têtes)
            const firstLine = lines[0];
            addResult('info', `🔍 Première ligne : "${firstLine}"`);
            
            // Détection du séparateur
            const commaCount = (firstLine.match(/,/g) || []).length;
            const semicolonCount = (firstLine.match(/;/g) || []).length;
            const tabCount = (firstLine.match(/\t/g) || []).length;
            
            addResult('info', `📊 Analyse des séparateurs :`);
            addResult('info', `• Virgules (,) : ${commaCount}`);
            addResult('info', `• Points-virgules (;) : ${semicolonCount}`);
            addResult('info', `• Tabulations (\\t) : ${tabCount}`);
            
            let separator = ',';
            let separatorName = 'virgule';
            
            if (semicolonCount > commaCount && semicolonCount > tabCount) {
                separator = ';';
                separatorName = 'point-virgule';
            } else if (tabCount > commaCount && tabCount > semicolonCount) {
                separator = '\t';
                separatorName = 'tabulation';
            }
            
            addResult('success', `✅ Séparateur détecté : ${separatorName} (${separator === '\t' ? '\\t' : separator})`);
            
            // Analyse des en-têtes
            const headers = firstLine.split(separator).map(h => h.trim().replace(/"/g, ''));
            addResult('success', `📋 En-têtes détectés (${headers.length}) :`);
            
            let headersTable = '<table><tr><th>Index</th><th>Nom de colonne</th><th>Statut</th></tr>';
            const requiredColumns = ['dateInpute', 'BookingDATE', 'typeClient'];
            
            headers.forEach((header, index) => {
                const isRequired = requiredColumns.includes(header);
                const status = isRequired ? '✅ Requis' : '📝 Optionnel';
                headersTable += `<tr><td>${index + 1}</td><td>${header}</td><td>${status}</td></tr>`;
            });
            headersTable += '</table>';
            
            results.innerHTML += `<div class="result info">${headersTable}</div>`;
            
            // Vérification des colonnes requises
            const missingColumns = requiredColumns.filter(col => !headers.includes(col));
            if (missingColumns.length > 0) {
                addResult('error', `❌ Colonnes manquantes : ${missingColumns.join(', ')}`);
            } else {
                addResult('success', '✅ Toutes les colonnes requises sont présentes');
            }
            
            // Analyse de quelques lignes de données
            addResult('info', '📊 Analyse des données (5 premières lignes) :');
            
            let dataTable = '<table><tr>';
            headers.forEach(header => {
                dataTable += `<th>${header}</th>`;
            });
            dataTable += '</tr>';
            
            for (let i = 1; i <= Math.min(6, lines.length - 1); i++) {
                const values = lines[i].split(separator).map(v => v.trim().replace(/"/g, ''));
                dataTable += '<tr>';
                values.forEach((value, index) => {
                    const header = headers[index] || `Col${index + 1}`;
                    let cellClass = '';
                    
                    if (header === 'dateInpute' || header === 'BookingDATE') {
                        const isValidDate = parseDate(value);
                        cellClass = isValidDate ? 'style="background: #d4edda;"' : 'style="background: #f8d7da;"';
                    }
                    
                    dataTable += `<td ${cellClass}>${value || '<vide>'}</td>`;
                });
                dataTable += '</tr>';
            }
            dataTable += '</table>';
            
            results.innerHTML += `<div class="result info">${dataTable}</div>`;
            
            // Test de parsing des dates
            addResult('info', '📅 Test de parsing des dates :');
            
            const dateInputeIndex = headers.indexOf('dateInpute');
            const bookingDateIndex = headers.indexOf('BookingDATE');
            
            if (dateInputeIndex !== -1) {
                for (let i = 1; i <= Math.min(4, lines.length - 1); i++) {
                    const values = lines[i].split(separator);
                    const dateValue = values[dateInputeIndex]?.trim().replace(/"/g, '');
                    const parsedDate = parseDate(dateValue);
                    
                    if (parsedDate) {
                        addResult('success', `✅ Ligne ${i} - dateInpute: "${dateValue}" → ${parsedDate.toLocaleString('fr-FR')}`);
                    } else {
                        addResult('error', `❌ Ligne ${i} - dateInpute: "${dateValue}" → Format invalide`);
                    }
                }
            }
            
            // Génération du code de correction
            addResult('info', '🔧 Code de correction suggéré :');
            
            let correctionCode = `
// Fonction de parsing améliorée pour votre fichier
function parseCSV(text) {
    const lines = text.split('\\n');
    const separator = '${separator}'; // Séparateur détecté
    const headers = lines[0].split(separator).map(h => h.trim().replace(/"/g, ''));
    const data = [];
    
    for (let i = 1; i < lines.length; i++) {
        if (lines[i].trim()) {
            const values = lines[i].split(separator).map(v => v.trim().replace(/"/g, ''));
            const row = {};
            headers.forEach((header, index) => {
                row[header] = values[index] || '';
            });
            data.push(row);
        }
    }
    
    return data;
}`;
            
            results.innerHTML += `<div class="result info"><pre>${correctionCode}</pre></div>`;
        }

        function addResult(type, message) {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function parseDate(dateStr) {
            if (!dateStr) return null;
            
            const formats = [
                /^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{2})$/,
                /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,
                /^(\d{4})-(\d{1,2})-(\d{1,2})T(\d{1,2}):(\d{2})$/,
                /^(\d{4})-(\d{1,2})-(\d{1,2})$/
            ];
            
            for (let format of formats) {
                const match = dateStr.toString().match(format);
                if (match) {
                    if (format === formats[2] || format === formats[3]) {
                        return new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]), 
                                      parseInt(match[4] || 0), parseInt(match[5] || 0));
                    } else {
                        const day = parseInt(match[1]);
                        const month = parseInt(match[2]) - 1;
                        const year = parseInt(match[3]);
                        const hour = match[4] ? parseInt(match[4]) : 0;
                        const minute = match[5] ? parseInt(match[5]) : 0;
                        return new Date(year, month, day, hour, minute);
                    }
                }
            }
            
            return null;
        }
    </script>
</body>
</html>
