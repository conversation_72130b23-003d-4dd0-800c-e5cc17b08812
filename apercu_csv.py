#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv

def afficher_apercu_csv():
    """
    Affiche un aperçu détaillé du fichier output.csv
    """
    
    # Lire le fichier CSV
    with open('output.csv', 'r', encoding='utf-8') as csvfile:
        reader = csv.reader(csvfile)
        rows = list(reader)
    
    # Supprimer les lignes vides à la fin
    while rows and not any(cell.strip() for cell in rows[-1]):
        rows.pop()
    
    total_lines = len(rows)
    
    print("="*100)
    print("APERÇU DÉTAILLÉ DU FICHIER output.csv")
    print("="*100)
    print(f"Nombre total de lignes : {total_lines}")
    print(f"Format : CSV (Comma-Separated Values)")
    print()
    
    if not rows:
        print("Le fichier est vide.")
        return
    
    # Afficher l'en-tête
    print("EN-TÊTE (43 colonnes) :")
    print("-" * 80)
    header = rows[0]
    for i, col in enumerate(header, 1):
        print(f"{i:2d}. {col}")
    print()
    
    # Afficher les 10 premières lignes de données
    print("10 PREMIÈRES LIGNES DE DONNÉES :")
    print("-" * 80)
    data_rows = rows[1:]  # Exclure l'en-tête
    for i in range(min(10, len(data_rows))):
        row = data_rows[i]
        # Afficher seulement les 6 premiers champs pour éviter les lignes trop longues
        preview = ", ".join(row[:6])
        if len(row) > 6:
            preview += f", ... (+{len(row)-6} colonnes)"
        print(f"Ligne {i+1:2d}: {preview}")
    print()
    
    # Afficher les 5 dernières lignes
    print("5 DERNIÈRES LIGNES :")
    print("-" * 80)
    if len(data_rows) > 10:
        start_idx = max(0, len(data_rows) - 5)
        for i in range(start_idx, len(data_rows)):
            row = data_rows[i]
            preview = ", ".join(row[:6])
            if len(row) > 6:
                preview += f", ... (+{len(row)-6} colonnes)"
            line_number = i + 1
            print(f"Ligne {line_number:4d}: {preview}")
    else:
        print("(Toutes les lignes de données sont déjà affichées ci-dessus)")
    
    print()
    print("="*100)
    print("RÉSUMÉ DE LA CONVERSION :")
    print(f"✅ Fichier source : Nouveau Text Document.txt")
    print(f"✅ Fichier TSV créé : output.tsv")
    print(f"✅ Fichier CSV créé : output.csv")
    print(f"✅ Nombre de lignes traitées : {total_lines}")
    print(f"✅ Nombre de colonnes : {len(header) if rows else 0}")
    print("✅ Conversion terminée avec succès !")
    print("="*100)

if __name__ == "__main__":
    afficher_apercu_csv()
