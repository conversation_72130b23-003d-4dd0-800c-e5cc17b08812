#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import io

def convert_tsv_to_csv():
    """
    Convertit le fichier output.tsv en format CSV
    """
    
    # Lire le fichier TSV
    with open('output.tsv', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Supprimer les lignes vides à la fin
    while lines and lines[-1].strip() == '':
        lines.pop()
    
    # Préparer les données pour le CSV
    csv_data = []
    
    for line in lines:
        line = line.strip()
        if line:  # Ignorer les lignes vides
            # Diviser par tabulation
            fields = line.split('\t')
            csv_data.append(fields)
    
    # Écrire le fichier CSV
    with open('output.csv', 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile, delimiter=',', quotechar='"', quoting=csv.QUOTE_MINIMAL)
        
        for row in csv_data:
            writer.writerow(row)
    
    print(f"Conversion TSV vers CSV terminée !")
    print(f"Nombre de lignes traitées : {len(csv_data)}")
    print("Fichier de sortie : output.csv")
    
    # Afficher un aperçu du CSV
    print("\n" + "="*80)
    print("APERÇU DU FICHIER output.csv")
    print("="*80)
    
    # En-tête
    if csv_data:
        print("\nEN-TÊTE :")
        print(",".join(csv_data[0]))
        
        # 10 premières lignes de données
        print(f"\n10 PREMIÈRES LIGNES DE DONNÉES :")
        data_rows = csv_data[1:]
        for i in range(min(10, len(data_rows))):
            # Afficher seulement les premiers champs pour éviter les lignes trop longues
            row_preview = ",".join(data_rows[i][:8]) + ("..." if len(data_rows[i]) > 8 else "")
            print(f"Ligne {i+1:2d}: {row_preview}")
        
        # 5 dernières lignes
        print(f"\n5 DERNIÈRES LIGNES :")
        if len(data_rows) > 10:
            start_idx = max(0, len(data_rows) - 5)
            for i in range(start_idx, len(data_rows)):
                row_preview = ",".join(data_rows[i][:8]) + ("..." if len(data_rows[i]) > 8 else "")
                line_number = i + 1
                print(f"Ligne {line_number:4d}: {row_preview}")
        else:
            print("(Toutes les lignes de données sont déjà affichées ci-dessus)")
    
    print(f"\nNombre total de lignes : {len(csv_data)}")
    print("Format : CSV (valeurs séparées par des virgules)")

if __name__ == "__main__":
    convert_tsv_to_csv()
