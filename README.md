# 📅 Calculateur de Jours Ouvrés - Sénégal

Une application web moderne pour calculer automatiquement le nombre de jours ouvrés entre deux dates, en tenant compte des week-ends et des jours fériés sénégalais.

## 🎯 Objectif

Calculer le nombre de jours ouvrés entre :
- **Date de début** (dateInpute)
- **Date de fin** (BookingDATE)

En excluant :
- ❌ Les week-ends (samedi et dimanche)
- ❌ Les jours fériés sénégalais 2025

## 🚀 Fonctionnalités

### ✨ Interface moderne et intuitive
- Design responsive avec Bootstrap 5
- Drag & Drop pour l'upload de fichiers
- Animations et transitions fluides
- Statistiques en temps réel

### 📊 Formats supportés
- **CSV** (.csv)
- **Excel** (.xlsx, .xls)

### 📈 Statistiques automatiques
- Nombre total de lignes traitées
- Délai moyen en jours ouvrés
- Délai minimum et maximum
- Statut des demandes (Terminé/En cours/Erreur)

### 💾 Export Excel
- Export des résultats en format Excel
- Colonnes ajustées automatiquement
- Nom de fichier avec date

## 🏛️ Jours fériés 2025 (Sénégal)

| Date | Fête |
|------|------|
| 01/01/2025 | Jour de l'an |
| 31/03/2025 | Lundi de Pâques (Aïd al-Fitr/Korité) |
| 04/04/2025 | Fête de l'Indépendance |
| 01/05/2025 | Fête du Travail |
| 29/05/2025 | Ascension |
| 07/06/2025 | Aïd al-Adha (Tabaski) |
| 09/06/2025 | Lundi de Pentecôte |
| 26/06/2025 | Ashura (Tamkharit) |
| 15/08/2025 | Assomption |
| 05/09/2025 | Anniversaire du Prophète (Mouloud) |
| 01/11/2025 | Toussaint |
| 25/12/2025 | Noël |

## 📋 Format des données

### Colonnes requises :
- **dateInpute** : Date de début (DD/MM/YYYY HH:MM ou DD/MM/YYYY)
- **BookingDATE** : Date de fin (DD/MM/YYYY HH:MM ou DD/MM/YYYY)

### Exemple de fichier CSV :
```csv
dateInpute,BookingDATE
29/04/2025 09:47,06/05/2025 15:44
29/04/2025 10:53,02/05/2025 13:27
30/04/2025 11:28,
```

> **Note :** Si BookingDATE est vide, la date d'aujourd'hui sera utilisée automatiquement.

## 🔧 Installation et utilisation

### 1. Télécharger les fichiers
```
index.html
script.js
exemple_donnees.csv
README.md
```

### 2. Ouvrir l'application
- Double-cliquer sur `index.html`
- Ou ouvrir dans un navigateur web moderne

### 3. Utiliser l'application
1. **Importer** : Glissez-déposez votre fichier CSV/Excel ou cliquez pour le sélectionner
2. **Traitement** : L'application calcule automatiquement les jours ouvrés
3. **Résultats** : Consultez les statistiques et le tableau des résultats
4. **Export** : Cliquez sur "Exporter en Excel" pour télécharger les résultats

## 🧮 Logique de calcul

### Formule équivalente Excel :
```excel
=SI(NB.VIDE(BookingDATE); 
    NB.JOURS.OUVRES.INTL(dateInpute; AUJOURDHUI(); 1; holiday_list); 
    NB.JOURS.OUVRES.INTL(dateInpute; BookingDATE; 1; holiday_list))
```

### Algorithme JavaScript :
1. Parse des dates au format DD/MM/YYYY HH:MM
2. Itération jour par jour entre les deux dates
3. Exclusion des week-ends (samedi=6, dimanche=0)
4. Exclusion des jours fériés sénégalais
5. Comptage des jours ouvrés restants

## 🌐 Compatibilité

### Navigateurs supportés :
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

### Technologies utilisées :
- **HTML5** : Structure de l'application
- **CSS3** : Styles et animations
- **JavaScript ES6+** : Logique métier
- **Bootstrap 5** : Framework CSS
- **Font Awesome** : Icônes
- **SheetJS** : Lecture/écriture Excel

## 📝 Exemples d'utilisation

### Cas 1 : Demande terminée
- **Date début** : 29/04/2025 09:47
- **Date fin** : 06/05/2025 15:44
- **Résultat** : 5 jours ouvrés (excluant le 1er mai)

### Cas 2 : Demande en cours
- **Date début** : 13/05/2025 16:58
- **Date fin** : (vide)
- **Résultat** : Calcul jusqu'à aujourd'hui

## 🐛 Gestion d'erreurs

L'application gère automatiquement :
- ❌ Formats de date invalides
- ❌ Colonnes manquantes
- ❌ Fichiers corrompus
- ❌ Dates incohérentes (fin < début)

## 📞 Support

Pour toute question ou problème :
1. Vérifiez le format de vos données
2. Consultez les exemples fournis
3. Testez avec le fichier `exemple_donnees.csv`

## 🔄 Mises à jour

### Version 1.0 (Actuelle)
- ✅ Calcul des jours ouvrés
- ✅ Support CSV/Excel
- ✅ Interface moderne
- ✅ Export Excel
- ✅ Jours fériés Sénégal 2025

### Prochaines versions
- 🔄 Support multi-années
- 🔄 Personnalisation des jours fériés
- 🔄 Graphiques et visualisations
- 🔄 API REST
